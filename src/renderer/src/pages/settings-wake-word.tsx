import { Control, ControlGroup } from "@renderer/components/ui/control"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@renderer/components/ui/select"
import { Switch } from "@renderer/components/ui/switch"
import { Button } from "@renderer/components/ui/button"
import { Input } from "@renderer/components/ui/input"
import { Slider } from "@renderer/components/ui/slider"
import { Badge } from "@renderer/components/ui/badge"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { tipcClient } from "@renderer/lib/tipc-client"
import { useState, useEffect } from "react"
import { toast } from "sonner"
import { WakeWordConfig } from "@shared/types"

export function Component() {
  const queryClient = useQueryClient()
  const [isDetecting, setIsDetecting] = useState(false)
  const [showDebug, setShowDebug] = useState(false)

  const configQuery = useQuery({
    queryKey: ["config"],
    queryFn: () => tipcClient.getConfig(),
  })

  const wakeWordStatusQuery = useQuery({
    queryKey: ["wakeWordStatus"],
    queryFn: () => tipcClient.getWakeWordStatus(),
    refetchInterval: 2000, // Poll every 2 seconds for status updates
  })

  const availableWakeWordsQuery = useQuery({
    queryKey: ["availableWakeWords"],
    queryFn: () => tipcClient.getAvailableWakeWords(),
  })

  // Debug queries
  const recentErrorsQuery = useQuery({
    queryKey: ["recentErrors"],
    queryFn: () => tipcClient.getRecentErrors({ count: 20 }),
    refetchInterval: showDebug ? 3000 : false, // Only poll when debug is open
  })

  const diagnosticReportQuery = useQuery({
    queryKey: ["diagnosticReport"],
    queryFn: () => tipcClient.getDiagnosticReport(),
    refetchInterval: showDebug ? 5000 : false, // Only poll when debug is open
  })

  const wakeWordDebugQuery = useQuery({
    queryKey: ["wakeWordDebug"],
    queryFn: () => tipcClient.getWakeWordDebugInfo(),
    refetchInterval: showDebug ? 2000 : false, // Poll more frequently for real-time data
  })

  const microphonePermissionQuery = useQuery({
    queryKey: ["microphonePermissions"],
    queryFn: () => tipcClient.checkMicrophonePermissions(),
    enabled: showDebug, // Only check when debug panel is open
    refetchInterval: showDebug ? 10000 : false, // Check every 10 seconds
  })

  const updateSettingsMutation = useMutation({
    mutationFn: (settings: Partial<WakeWordConfig>) =>
      tipcClient.updateWakeWordSettings({ settings }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["config"] })
      queryClient.invalidateQueries({ queryKey: ["wakeWordStatus"] })
      toast.success("Wake word settings updated")
    },
    onError: (error: any) => {
      toast.error(`Failed to update settings: ${error.error || error.message}`)
    },
  })

  const startDetectionMutation = useMutation({
    mutationFn: () => tipcClient.startWakeWordDetection(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["wakeWordStatus"] })
      toast.success("Wake word detection started")
    },
    onError: (error: any) => {
      toast.error(`Failed to start detection: ${error.error || error.message}`)
    },
  })

  const stopDetectionMutation = useMutation({
    mutationFn: () => tipcClient.stopWakeWordDetection(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["wakeWordStatus"] })
      toast.success("Wake word detection stopped")
    },
    onError: (error: any) => {
      toast.error(`Failed to stop detection: ${error.error || error.message}`)
    },
  })

  const initializeMutation = useMutation({
    mutationFn: () => tipcClient.initializeWakeWordService(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["wakeWordStatus"] })
      toast.success("Wake word service initialized")
    },
    onError: (error: any) => {
      toast.error(`Failed to initialize: ${error.error || error.message}`)
    },
  })

  useEffect(() => {
    if (wakeWordStatusQuery.data) {
      setIsDetecting(wakeWordStatusQuery.data.isActive)
    }
  }, [wakeWordStatusQuery.data])

  const saveConfig = (updates: Partial<WakeWordConfig>) => {
    if (!configQuery.data) return

    const newConfig = {
      ...configQuery.data,
      wakeWord: {
        ...configQuery.data.wakeWord,
        ...updates,
      },
    }

    tipcClient.saveConfig({ config: newConfig }).then(() => {
      queryClient.invalidateQueries({ queryKey: ["config"] })
    })
  }

  const wakeWordConfig = configQuery.data?.wakeWord || {}
  const isEnabled = wakeWordConfig.enabled || false

  if (configQuery.isLoading) {
    return <div className="p-6">Loading...</div>
  }

  return (
    <div className="h-full overflow-auto px-6 py-4 liquid-glass-panel">
      <header className="mb-5 liquid-glass-card glass-border rounded-lg p-4 glass-shadow">
        <h2 className="text-2xl font-bold">Wake Word</h2>
        <p className="text-sm text-muted-foreground mt-1">
          Configure hands-free voice activation with wake word detection
        </p>
      </header>

      <div className="grid gap-4">
        <ControlGroup title="Wake Word Detection">
          <Control label="Enable Wake Word Detection" className="px-3">
            <div className="flex items-center gap-2">
              <Switch
                checked={isEnabled}
                onCheckedChange={(checked) => {
                  saveConfig({ enabled: checked })
                  if (checked) {
                    updateSettingsMutation.mutate({ enabled: checked })
                  }
                }}
              />
              {wakeWordStatusQuery.data?.isActive && (
                <Badge variant="secondary" className="text-xs">
                  Active
                </Badge>
              )}
            </div>
          </Control>

          {isEnabled && (
            <>
              <Control label="Wake Word" className="px-3">
                <Select
                  value={wakeWordConfig.wakeWord || "hey computer"}
                  onValueChange={(value) => {
                    saveConfig({ wakeWord: value as any })
                    updateSettingsMutation.mutate({ wakeWord: value as any })
                  }}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {availableWakeWordsQuery.data?.map((word) => (
                      <SelectItem key={word} value={word}>
                        {word}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </Control>

              <Control label="Sensitivity" className="px-3">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">
                      {((wakeWordConfig.sensitivity || 0.5) * 100).toFixed(0)}%
                    </span>
                    <span className="text-xs text-muted-foreground">
                      Higher = more sensitive
                    </span>
                  </div>
                  <Slider
                    value={[(wakeWordConfig.sensitivity || 0.5) * 100]}
                    onValueChange={([value]) => {
                      const sensitivity = value / 100
                      saveConfig({ sensitivity })
                      updateSettingsMutation.mutate({ sensitivity })
                    }}
                    max={100}
                    min={10}
                    step={5}
                    className="w-full"
                  />
                </div>
              </Control>

              <Control label="Recording Timeout" className="px-3">
                <div className="flex items-center gap-2">
                  <Input
                    type="number"
                    min={1}
                    max={30}
                    value={wakeWordConfig.recordingTimeout || 5}
                    onChange={(e) => {
                      const timeout = parseInt(e.target.value) || 5
                      saveConfig({ recordingTimeout: timeout })
                      updateSettingsMutation.mutate({ recordingTimeout: timeout })
                    }}
                    className="w-20"
                  />
                  <span className="text-sm text-muted-foreground">seconds</span>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Time to wait before resuming detection after recording
                </p>
              </Control>

              <Control label="Confirmation Mode" className="px-3">
                <Switch
                  checked={wakeWordConfig.confirmationMode || false}
                  onCheckedChange={(checked) => {
                    saveConfig({ confirmationMode: checked })
                    updateSettingsMutation.mutate({ confirmationMode: checked })
                  }}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Show confirmation dialog before starting recording
                </p>
              </Control>
            </>
          )}
        </ControlGroup>

        {isEnabled && (
          <ControlGroup title="Detection Control">
            <Control label="Manual Control" className="px-3">
              <div className="flex gap-2">
                <Button
                  onClick={() => startDetectionMutation.mutate()}
                  disabled={isDetecting || startDetectionMutation.isPending}
                  size="sm"
                >
                  Start Detection
                </Button>
                <Button
                  onClick={() => stopDetectionMutation.mutate()}
                  disabled={!isDetecting || stopDetectionMutation.isPending}
                  variant="outline"
                  size="sm"
                >
                  Stop Detection
                </Button>
                <Button
                  onClick={() => initializeMutation.mutate()}
                  disabled={initializeMutation.isPending}
                  variant="outline"
                  size="sm"
                >
                  Reinitialize
                </Button>
              </div>
            </Control>

            <Control label="Status" className="px-3">
              <div className="text-sm">
                {isDetecting ? (
                  <span className="text-green-600">🎤 Listening for wake word...</span>
                ) : (
                  <span className="text-muted-foreground">⏸️ Detection stopped</span>
                )}
              </div>
            </Control>
          </ControlGroup>
        )}

        <ControlGroup title="Web Speech API">
          <div className="px-3 py-2 bg-green-50 dark:bg-green-950/20 rounded-lg">
            <p className="text-sm text-green-800 dark:text-green-200">
              <strong>Powered by Web Speech API:</strong> This implementation uses your browser's built-in speech recognition for wake word detection.
            </p>
            <ul className="text-xs text-green-700 dark:text-green-300 mt-2 ml-4 list-disc space-y-1">
              <li>No external dependencies or API keys required</li>
              <li>Works completely offline and locally</li>
              <li>Uses your system's native speech recognition</li>
              <li>Supports custom wake phrases</li>
            </ul>
          </div>
        </ControlGroup>

        <ControlGroup title="Privacy & Performance">
          <div className="px-3 space-y-2 text-sm text-muted-foreground">
            <div className="flex items-start gap-2">
              <span className="text-green-600">✅</span>
              <span>All processing happens locally on your device</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="text-green-600">✅</span>
              <span>No audio data is sent to external servers</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="text-green-600">✅</span>
              <span>You have full control to disable at any time</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="text-green-600">✅</span>
              <span>Minimal system resource usage</span>
            </div>
          </div>
        </ControlGroup>

        {/* Debug Section */}
        <ControlGroup title="Debug Information">
          <Control label="Show Debug Panel" className="px-3">
            <div className="flex items-center gap-2">
              <Switch
                checked={showDebug}
                onCheckedChange={setShowDebug}
              />
              <span className="text-sm text-muted-foreground">
                View detailed logs and diagnostics
              </span>
            </div>
          </Control>

          {showDebug && (
            <div className="px-3 space-y-4">
              {/* Wake Word Status */}
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Current Status</h4>
                <div className="bg-muted/50 rounded-lg p-3 space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Detection Active:</span>
                    <Badge variant={isDetecting ? "default" : "secondary"}>
                      {isDetecting ? "Yes" : "No"}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Recognition Window:</span>
                    <Badge variant={wakeWordDebugQuery.data?.recognitionWindowExists ? "default" : "secondary"}>
                      {wakeWordDebugQuery.data?.recognitionWindowExists ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Speech Recognition:</span>
                    <Badge variant={microphonePermissionQuery.data?.hasPermission ? "default" : "destructive"}>
                      {microphonePermissionQuery.data?.hasPermission ? "Available" : "Unavailable"}
                    </Badge>
                  </div>
                  {microphonePermissionQuery.data?.error && (
                    <div className="text-xs text-muted-foreground bg-muted/50 p-2 rounded">
                      <strong>Status:</strong> {microphonePermissionQuery.data.error}
                      {microphonePermissionQuery.data.details && (
                        <div className="mt-1 font-mono text-xs">
                          {JSON.stringify(microphonePermissionQuery.data.details, null, 2)}
                        </div>
                      )}
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span>Wake Word:</span>
                    <span className="font-mono">{wakeWordConfig.wakeWord || "hey computer"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Sensitivity:</span>
                    <span>{((wakeWordConfig.sensitivity || 0.5) * 100).toFixed(0)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Recording Timeout:</span>
                    <span>{wakeWordConfig.recordingTimeout || 5}s</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total Detections:</span>
                    <span>{wakeWordDebugQuery.data?.detectionEvents?.length || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Recent Transcripts:</span>
                    <span>{wakeWordDebugQuery.data?.recentTranscripts?.length || 0}</span>
                  </div>
                </div>
              </div>

              {/* Real-time Transcripts */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-sm">Live Transcripts</h4>
                  <Button
                    onClick={() => {
                      wakeWordDebugQuery.refetch()
                      recentErrorsQuery.refetch()
                    }}
                    size="sm"
                    variant="outline"
                  >
                    Refresh
                  </Button>
                </div>
                <div className="bg-muted/50 rounded-lg p-3 max-h-48 overflow-y-auto">
                  {wakeWordDebugQuery.data?.recentTranscripts && wakeWordDebugQuery.data.recentTranscripts.length > 0 ? (
                    <div className="space-y-2">
                      {wakeWordDebugQuery.data.recentTranscripts
                        .slice(-10)
                        .reverse()
                        .map((transcript: any, index: number) => (
                          <div key={index} className="text-xs border-l-2 border-l-green-500 pl-2">
                            <div className="flex items-center gap-2">
                              <Badge
                                variant={transcript.matched ? 'default' : 'secondary'}
                                className="text-xs"
                              >
                                {transcript.matched ? 'MATCH' : 'transcript'}
                              </Badge>
                              <span className="text-muted-foreground">
                                {new Date(transcript.timestamp).toLocaleTimeString()}
                              </span>
                            </div>
                            <div className="mt-1 font-mono text-xs">"{transcript.transcript}"</div>
                          </div>
                        ))}
                    </div>
                  ) : (
                    <div className="text-xs text-muted-foreground text-center py-4">
                      {isDetecting ? "Listening for speech..." : "Start detection to see transcripts"}
                    </div>
                  )}
                </div>
              </div>

              {/* Detection Events */}
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Wake Word Detections</h4>
                <div className="bg-muted/50 rounded-lg p-3 max-h-48 overflow-y-auto">
                  {wakeWordDebugQuery.data?.detectionEvents && wakeWordDebugQuery.data.detectionEvents.length > 0 ? (
                    <div className="space-y-2">
                      {wakeWordDebugQuery.data.detectionEvents
                        .slice(-10)
                        .reverse()
                        .map((event: any, index: number) => (
                          <div key={index} className="text-xs border-l-2 border-l-orange-500 pl-2">
                            <div className="flex items-center gap-2">
                              <Badge variant="default" className="text-xs">
                                DETECTED
                              </Badge>
                              <span className="text-muted-foreground">
                                {new Date(event.timestamp).toLocaleTimeString()}
                              </span>
                              <span className="text-xs text-muted-foreground">
                                confidence: {(event.confidence * 100).toFixed(0)}%
                              </span>
                            </div>
                            <div className="mt-1 font-mono text-xs">"{event.wakeWord}"</div>
                          </div>
                        ))}
                    </div>
                  ) : (
                    <div className="text-xs text-muted-foreground text-center py-4">
                      No wake word detections yet
                    </div>
                  )}
                </div>
              </div>

              {/* Recent Errors */}
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Recent Logs & Errors</h4>
                <div className="bg-muted/50 rounded-lg p-3 max-h-48 overflow-y-auto">
                  {recentErrorsQuery.data && recentErrorsQuery.data.length > 0 ? (
                    <div className="space-y-2">
                      {recentErrorsQuery.data
                        .filter((error: any) => error.component === 'wake-word-service')
                        .slice(-10)
                        .reverse()
                        .map((error: any, index: number) => (
                          <div key={index} className="text-xs border-l-2 border-l-blue-500 pl-2">
                            <div className="flex items-center gap-2">
                              <Badge
                                variant={error.level === 'error' ? 'destructive' :
                                        error.level === 'warning' ? 'secondary' : 'default'}
                                className="text-xs"
                              >
                                {error.level}
                              </Badge>
                              <span className="text-muted-foreground">
                                {new Date(error.timestamp).toLocaleTimeString()}
                              </span>
                            </div>
                            <div className="mt-1 font-mono text-xs">{error.message}</div>
                          </div>
                        ))}
                      {recentErrorsQuery.data.filter((error: any) => error.component === 'wake-word-service').length === 0 && (
                        <div className="text-xs text-muted-foreground text-center py-4">
                          No wake word logs found
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-xs text-muted-foreground text-center py-4">
                      No recent logs available
                    </div>
                  )}
                </div>
              </div>

              {/* System Information */}
              <div className="space-y-2">
                <h4 className="font-medium text-sm">System Information</h4>
                <div className="bg-muted/50 rounded-lg p-3 space-y-2 text-xs">
                  {diagnosticReportQuery.data && (
                    <>
                      <div className="flex justify-between">
                        <span>Platform:</span>
                        <span className="font-mono">{diagnosticReportQuery.data.system.platform}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Node Version:</span>
                        <span className="font-mono">{diagnosticReportQuery.data.system.nodeVersion}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Electron Version:</span>
                        <span className="font-mono">{diagnosticReportQuery.data.system.electronVersion}</span>
                      </div>
                    </>
                  )}
                </div>
              </div>

              {/* Troubleshooting Tips */}
              {(!microphonePermissionQuery.data?.hasPermission ||
                (wakeWordDebugQuery.data?.recentTranscripts?.length === 0 && isDetecting)) && (
                <div className="space-y-2">
                  <h4 className="font-medium text-sm text-orange-600">Troubleshooting</h4>
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 text-sm space-y-2">
                    {!microphonePermissionQuery.data?.hasPermission && (
                      <div className="flex items-start gap-2">
                        <span className="text-orange-600">⚠️</span>
                        <div>
                          <strong>Microphone Access Denied:</strong> The app needs microphone permission to detect wake words.
                          Please allow microphone access in your browser/system settings and restart the app.
                        </div>
                      </div>
                    )}
                    {isDetecting && wakeWordDebugQuery.data?.recentTranscripts?.length === 0 && (
                      <div className="flex items-start gap-2">
                        <span className="text-orange-600">🎤</span>
                        <div>
                          <strong>No Speech Detected:</strong> Try speaking louder, check your microphone is working,
                          or ensure you're using a supported browser (Chrome/Edge recommended).
                        </div>
                      </div>
                    )}
                    <div className="flex items-start gap-2">
                      <span className="text-blue-600">💡</span>
                      <div>
                        <strong>Tips:</strong> Web Speech API works best in Chrome/Edge. Make sure your microphone is not muted
                        and no other apps are using it exclusively.
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Debug Actions */}
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Debug Actions</h4>
                <div className="flex gap-2 flex-wrap">
                  <Button
                    onClick={async () => {
                      const result = await tipcClient.simulateWakeWordDetection()
                      if (result.success) {
                        wakeWordDebugQuery.refetch()
                        toast.success("Simulated wake word detection")
                      } else {
                        toast.error(`Failed to simulate: ${result.error}`)
                      }
                    }}
                    size="sm"
                    variant="default"
                  >
                    Test Detection
                  </Button>
                  <Button
                    onClick={async () => {
                      microphonePermissionQuery.refetch()
                      toast.success("Speech recognition status checked")
                    }}
                    size="sm"
                    variant="outline"
                  >
                    Check Speech API
                  </Button>
                  <Button
                    onClick={async () => {
                      try {
                        const result = await tipcClient.testSpeechRecognition()
                        if (result.success) {
                          toast.success("Speech recognition test passed!")
                        } else {
                          toast.error(`Speech recognition test failed: ${result.error}`)
                        }
                      } catch (error) {
                        toast.error(`Test error: ${error}`)
                      }
                    }}
                    size="sm"
                    variant="outline"
                  >
                    Test Speech Recognition
                  </Button>
                  <Button
                    onClick={async () => {
                      await tipcClient.clearWakeWordDebugHistory()
                      wakeWordDebugQuery.refetch()
                      toast.success("Wake word debug history cleared")
                    }}
                    size="sm"
                    variant="outline"
                  >
                    Clear Debug History
                  </Button>
                  <Button
                    onClick={() => tipcClient.clearErrorLog()}
                    size="sm"
                    variant="outline"
                  >
                    Clear All Logs
                  </Button>
                  <Button
                    onClick={async () => {
                      try {
                        const result = await tipcClient.saveDiagnosticReport({})
                        if (result.success) {
                          toast.success(`Diagnostic report saved to: ${result.filePath}`)
                        } else {
                          toast.error(`Failed to save report: ${result.error}`)
                        }
                      } catch (error) {
                        toast.error(`Error saving report: ${error}`)
                      }
                    }}
                    size="sm"
                    variant="outline"
                  >
                    Save Diagnostic Report
                  </Button>
                  <Button
                    onClick={() => {
                      const debugData = {
                        config: wakeWordConfig,
                        status: wakeWordStatusQuery.data,
                        debugInfo: wakeWordDebugQuery.data
                      }
                      const data = JSON.stringify(debugData, null, 2)
                      navigator.clipboard.writeText(data)
                      toast.success("Debug data copied to clipboard")
                    }}
                    size="sm"
                    variant="outline"
                  >
                    Copy Debug Data
                  </Button>
                </div>
              </div>
            </div>
          )}
        </ControlGroup>
      </div>
    </div>
  )
}
