import { EventEmitter } from "events"
import { configStore } from "./config"
import { WakeWordConfig } from "../shared/types"
import { diagnosticsService } from "./diagnostics"
import { BrowserWindow } from "electron"
import { WINDOWS } from "./window"

export interface WakeWordDetectionEvent {
  wakeWord: string
  timestamp: number
  confidence?: number
}

export class WakeWordService extends EventEmitter {
  private isActive = false
  private isDetecting = false
  private config: WakeWordConfig = {}
  private recognitionWindow?: BrowserWindow
  private detectionEvents: WakeWordDetectionEvent[] = []
  private recentTranscripts: Array<{ transcript: string; timestamp: number; matched: boolean }> = []
  private maxEventHistory = 50

  constructor() {
    super()
    this.updateConfig()
  }

  private updateConfig() {
    const appConfig = configStore.get()
    this.config = appConfig.wakeWord || {}
  }

  async initialize(): Promise<void> {
    try {
      this.updateConfig()

      if (!this.config.enabled) {
        return
      }

      // Create a hidden window for Web Speech API
      await this.createRecognitionWindow()

      console.log("Wake word service initialized with Web Speech API")
      diagnosticsService.logInfo("wake-word-service", "Initialized with Web Speech API")

    } catch (error) {
      diagnosticsService.logError("wake-word-service", "Failed to initialize", error)
      throw error
    }
  }

  async startDetection(): Promise<void> {
    if (this.isDetecting) {
      return
    }

    try {
      this.updateConfig()

      if (!this.config.enabled) {
        throw new Error("Wake word detection is disabled")
      }

      if (!this.recognitionWindow) {
        await this.createRecognitionWindow()
      }

      this.isDetecting = true
      this.emit("detectionStarted")

      // Start Web Speech API recognition
      await this.startWebSpeechRecognition()

    } catch (error) {
      this.isDetecting = false
      diagnosticsService.logError("wake-word-service", "Failed to start detection", error)
      this.emit("detectionError", error)
      throw error
    }
  }

  private async createRecognitionWindow(): Promise<void> {
    if (this.recognitionWindow && !this.recognitionWindow.isDestroyed()) {
      return
    }

    this.recognitionWindow = new BrowserWindow({
      width: 1,
      height: 1,
      show: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        sandbox: false,
        // Enable media access for Web Speech API
        allowRunningInsecureContent: true,
        experimentalFeatures: true,
      },
    })

    // Set permissions for microphone access
    this.recognitionWindow.webContents.session.setPermissionRequestHandler((webContents, permission, callback) => {
      if (permission === 'microphone') {
        callback(true) // Grant microphone permission
      } else {
        callback(false)
      }
    })

    // Load a minimal HTML page for Web Speech API
    await this.recognitionWindow.loadURL(`data:text/html,
      <!DOCTYPE html>
      <html>
        <head><title>Wake Word Recognition</title></head>
        <body>
          <script>
            let recognition = null;
            let isListening = false;

            window.electronAPI = {
              startRecognition: (config) => {
                if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
                  throw new Error('Web Speech API not supported');
                }

                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                recognition = new SpeechRecognition();

                recognition.continuous = true;
                recognition.interimResults = true;
                recognition.lang = 'en-US';

                recognition.onresult = (event) => {
                  for (let i = event.resultIndex; i < event.results.length; i++) {
                    const transcript = event.results[i][0].transcript.toLowerCase().trim();
                    const matched = transcript.includes(config.wakeWord.toLowerCase());

                    // Log all transcripts for debugging
                    window.postMessage({
                      type: 'transcriptReceived',
                      data: {
                        transcript: transcript,
                        timestamp: Date.now(),
                        matched: matched,
                        confidence: event.results[i][0].confidence
                      }
                    }, '*');

                    if (matched) {
                      window.postMessage({
                        type: 'wakeWordDetected',
                        data: {
                          wakeWord: config.wakeWord,
                          transcript: transcript,
                          timestamp: Date.now(),
                          confidence: event.results[i][0].confidence || config.sensitivity
                        }
                      }, '*');
                    }
                  }
                };

                recognition.onerror = (event) => {
                  console.error('Web Speech API error:', event.error);
                  window.postMessage({
                    type: 'recognitionError',
                    data: {
                      error: event.error,
                      timestamp: Date.now(),
                      message: \`Recognition error: \${event.error}\`
                    }
                  }, '*');
                };

                recognition.onstart = () => {
                  console.log('Web Speech API recognition started');
                  window.postMessage({
                    type: 'recognitionStarted',
                    data: { timestamp: Date.now() }
                  }, '*');
                };

                recognition.onnomatch = () => {
                  console.log('Web Speech API: no match found');
                  window.postMessage({
                    type: 'noMatch',
                    data: { timestamp: Date.now() }
                  }, '*');
                };

                recognition.onsoundstart = () => {
                  console.log('Web Speech API: sound detected');
                  window.postMessage({
                    type: 'soundDetected',
                    data: { timestamp: Date.now() }
                  }, '*');
                };

                recognition.onspeechstart = () => {
                  console.log('Web Speech API: speech detected');
                  window.postMessage({
                    type: 'speechDetected',
                    data: { timestamp: Date.now() }
                  }, '*');
                };

                recognition.onend = () => {
                  if (isListening) {
                    // Restart recognition if we're still supposed to be listening
                    setTimeout(() => {
                      if (isListening && recognition) {
                        recognition.start();
                      }
                    }, 100);
                  }
                };

                isListening = true;
                recognition.start();
              },

              stopRecognition: () => {
                isListening = false;
                if (recognition) {
                  recognition.stop();
                  recognition = null;
                }
              }
            };
          </script>
        </body>
      </html>
    `)

    // Set up message handling
    this.recognitionWindow.webContents.on('console-message', (_event, _level, message) => {
      console.log('Recognition window:', message)
    })
  }

  private async startWebSpeechRecognition(): Promise<void> {
    if (!this.recognitionWindow || this.recognitionWindow.isDestroyed()) {
      throw new Error("Recognition window not available")
    }

    // Set up message listener for wake word detection
    this.recognitionWindow.webContents.on('did-finish-load', () => {
      this.recognitionWindow!.webContents.executeJavaScript(`
        window.addEventListener('message', (event) => {
          if (event.data.type === 'wakeWordDetected') {
            console.log('Wake word detected:', event.data.data);
          } else if (event.data.type === 'recognitionError') {
            console.error('Recognition error:', event.data.data);
          } else if (event.data.type === 'transcriptReceived') {
            console.log('Transcript received:', event.data.data);
          } else if (event.data.type === 'recognitionStarted') {
            console.log('Recognition started:', event.data.data);
          } else if (event.data.type === 'soundDetected') {
            console.log('Sound detected:', event.data.data);
          } else if (event.data.type === 'speechDetected') {
            console.log('Speech detected:', event.data.data);
          } else if (event.data.type === 'noMatch') {
            console.log('No match:', event.data.data);
          }
        });

        window.electronAPI.startRecognition({
          wakeWord: '${this.config.wakeWord || "hey computer"}',
          sensitivity: ${this.config.sensitivity || 0.5}
        });
      `)
    })

    // Handle messages from the recognition window
    this.recognitionWindow.webContents.on('console-message', (_event, _level, message) => {
      if (message.includes('Wake word detected:')) {
        try {
          const data = JSON.parse(message.split('Wake word detected: ')[1])
          const detectionEvent: WakeWordDetectionEvent = {
            wakeWord: data.wakeWord,
            timestamp: data.timestamp,
            confidence: data.confidence
          }

          // Store detection event for debug
          this.detectionEvents.push(detectionEvent)
          if (this.detectionEvents.length > this.maxEventHistory) {
            this.detectionEvents = this.detectionEvents.slice(-this.maxEventHistory)
          }

          console.log("Web Speech API wake word detected:", detectionEvent)
          diagnosticsService.logInfo("wake-word-service", `Wake word detected: ${data.wakeWord} (confidence: ${data.confidence})`)
          this.emit("wakeWordDetected", detectionEvent)

          // Pause detection for the configured timeout
          this.pauseDetection()
        } catch (error) {
          console.error("Failed to parse wake word detection data:", error)
          diagnosticsService.logError("wake-word-service", "Failed to parse wake word detection data", error)
        }
      } else if (message.includes('Transcript received:')) {
        try {
          const data = JSON.parse(message.split('Transcript received: ')[1])

          // Store transcript for debug
          this.recentTranscripts.push({
            transcript: data.transcript,
            timestamp: data.timestamp,
            matched: data.matched
          })
          if (this.recentTranscripts.length > this.maxEventHistory) {
            this.recentTranscripts = this.recentTranscripts.slice(-this.maxEventHistory)
          }

          diagnosticsService.logInfo("wake-word-service", `Transcript: "${data.transcript}" (matched: ${data.matched})`)
        } catch (error) {
          console.error("Failed to parse transcript data:", error)
        }
      } else if (message.includes('Recognition error:')) {
        console.error("Web Speech API error:", message)
        diagnosticsService.logError("wake-word-service", `Recognition error: ${message}`)
        this.emit("detectionError", new Error(message))
      } else if (message.includes('Recognition started:')) {
        console.log("Web Speech API recognition started")
        diagnosticsService.logInfo("wake-word-service", "Speech recognition started")
      } else if (message.includes('Sound detected:')) {
        console.log("Web Speech API sound detected")
        diagnosticsService.logInfo("wake-word-service", "Sound detected by microphone")
      } else if (message.includes('Speech detected:')) {
        console.log("Web Speech API speech detected")
        diagnosticsService.logInfo("wake-word-service", "Speech detected by recognition engine")
      } else if (message.includes('No match:')) {
        console.log("Web Speech API no match")
        diagnosticsService.logInfo("wake-word-service", "No speech match found")
      }
    })
  }

  async stopDetection(): Promise<void> {
    if (!this.isDetecting) {
      return
    }

    this.isDetecting = false

    // Stop Web Speech API recognition
    if (this.recognitionWindow && !this.recognitionWindow.isDestroyed()) {
      await this.recognitionWindow.webContents.executeJavaScript(`
        if (window.electronAPI && window.electronAPI.stopRecognition) {
          window.electronAPI.stopRecognition();
        }
      `)
    }

    this.emit("detectionStopped")
    diagnosticsService.logInfo("wake-word-service", "Detection stopped")
  }

  private async pauseDetection(): Promise<void> {
    const timeout = (this.config.recordingTimeout || 5) * 1000

    await this.stopDetection()

    setTimeout(async () => {
      if (this.config.enabled) {
        await this.startDetection()
      }
    }, timeout)
  }

  async cleanup(): Promise<void> {
    await this.stopDetection()

    // Close recognition window
    if (this.recognitionWindow && !this.recognitionWindow.isDestroyed()) {
      this.recognitionWindow.close()
      this.recognitionWindow = undefined
    }

    this.removeAllListeners()
    diagnosticsService.logInfo("wake-word-service", "Service cleaned up")
  }

  isDetectionActive(): boolean {
    return this.isDetecting
  }

  getConfig(): WakeWordConfig {
    return { ...this.config }
  }

  async updateSettings(newConfig: Partial<WakeWordConfig>): Promise<void> {
    const currentConfig = configStore.get()
    const updatedWakeWordConfig = {
      ...currentConfig.wakeWord,
      ...newConfig
    }

    configStore.save({
      ...currentConfig,
      wakeWord: updatedWakeWordConfig
    })

    this.updateConfig()

    // Restart detection if it was active and settings changed
    if (this.isDetecting) {
      await this.stopDetection()
      if (this.config.enabled) {
        await this.startDetection()
      }
    }
  }

  getAvailableWakeWords(): string[] {
    return [
      "hey computer",
      "hey assistant",
      "wake up",
      "listen up",
      "computer",
      "assistant",
      "hello computer",
      "hello assistant",
      "start listening",
      "activate",
      "voice command"
    ]
  }

  // Debug methods
  getDetectionEvents(): WakeWordDetectionEvent[] {
    return [...this.detectionEvents]
  }

  getRecentTranscripts(): Array<{ transcript: string; timestamp: number; matched: boolean }> {
    return [...this.recentTranscripts]
  }

  clearDebugHistory(): void {
    this.detectionEvents = []
    this.recentTranscripts = []
    diagnosticsService.logInfo("wake-word-service", "Debug history cleared")
  }

  getDebugInfo(): {
    isDetecting: boolean
    config: WakeWordConfig
    detectionEvents: WakeWordDetectionEvent[]
    recentTranscripts: Array<{ transcript: string; timestamp: number; matched: boolean }>
    recognitionWindowExists: boolean
  } {
    return {
      isDetecting: this.isDetecting,
      config: { ...this.config },
      detectionEvents: [...this.detectionEvents],
      recentTranscripts: [...this.recentTranscripts],
      recognitionWindowExists: !!this.recognitionWindow && !this.recognitionWindow.isDestroyed()
    }
  }

  // Check microphone permissions and Web Speech API availability
  async checkMicrophonePermissions(): Promise<{ hasPermission: boolean; error?: string; details?: any }> {
    if (!this.recognitionWindow || this.recognitionWindow.isDestroyed()) {
      return { hasPermission: false, error: "Recognition window not available" }
    }

    try {
      const result = await this.recognitionWindow.webContents.executeJavaScript(`
        (async () => {
          try {
            // Check if Web Speech API is available
            const hasSpeechRecognition = ('webkitSpeechRecognition' in window) || ('SpeechRecognition' in window);
            if (!hasSpeechRecognition) {
              return {
                hasPermission: false,
                error: 'Web Speech API not supported in this context',
                details: {
                  userAgent: navigator.userAgent,
                  webkitSpeechRecognition: 'webkitSpeechRecognition' in window,
                  SpeechRecognition: 'SpeechRecognition' in window
                }
              };
            }

            // Check if navigator.mediaDevices is available
            if (!navigator.mediaDevices) {
              return {
                hasPermission: false,
                error: 'MediaDevices API not available - this is normal for hidden windows',
                details: {
                  hasNavigator: !!navigator,
                  hasMediaDevices: !!navigator.mediaDevices,
                  protocol: window.location.protocol
                }
              };
            }

            // Try to create a SpeechRecognition instance instead of checking getUserMedia
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            const recognition = new SpeechRecognition();

            // If we can create it, assume permissions are OK since the main app has them
            return {
              hasPermission: true,
              details: {
                speechRecognitionAvailable: true,
                continuous: recognition.continuous !== undefined,
                interimResults: recognition.interimResults !== undefined
              }
            };
          } catch (error) {
            return {
              hasPermission: false,
              error: error.message,
              details: {
                errorName: error.name,
                errorStack: error.stack
              }
            };
          }
        })()
      `)
      return result
    } catch (error) {
      return {
        hasPermission: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        details: { executionError: true }
      }
    }
  }

  // Test speech recognition functionality
  async testSpeechRecognition(): Promise<{ success: boolean; error?: string; details?: any }> {
    if (!this.recognitionWindow || this.recognitionWindow.isDestroyed()) {
      return { success: false, error: "Recognition window not available" }
    }

    try {
      const result = await this.recognitionWindow.webContents.executeJavaScript(`
        (async () => {
          try {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            if (!SpeechRecognition) {
              return { success: false, error: 'SpeechRecognition not available' };
            }

            const recognition = new SpeechRecognition();
            recognition.continuous = false;
            recognition.interimResults = false;
            recognition.maxAlternatives = 1;

            return new Promise((resolve) => {
              let resolved = false;
              const timeout = setTimeout(() => {
                if (!resolved) {
                  resolved = true;
                  recognition.stop();
                  resolve({
                    success: true,
                    error: 'Test timeout - but recognition object created successfully',
                    details: { testType: 'creation_only' }
                  });
                }
              }, 1000);

              recognition.onstart = () => {
                if (!resolved) {
                  resolved = true;
                  clearTimeout(timeout);
                  recognition.stop();
                  resolve({
                    success: true,
                    details: { testType: 'started_successfully' }
                  });
                }
              };

              recognition.onerror = (event) => {
                if (!resolved) {
                  resolved = true;
                  clearTimeout(timeout);
                  resolve({
                    success: false,
                    error: \`Recognition error: \${event.error}\`,
                    details: { testType: 'error_on_start', errorType: event.error }
                  });
                }
              };

              try {
                recognition.start();
              } catch (error) {
                if (!resolved) {
                  resolved = true;
                  clearTimeout(timeout);
                  resolve({
                    success: false,
                    error: \`Failed to start recognition: \${error.message}\`,
                    details: { testType: 'start_exception' }
                  });
                }
              }
            });
          } catch (error) {
            return {
              success: false,
              error: error.message,
              details: { testType: 'setup_error' }
            };
          }
        })()
      `)
      return result
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        details: { testType: 'execution_error' }
      }
    }
  }

  // Test method for debugging
  simulateWakeWordDetection(): void {
    const testEvent: WakeWordDetectionEvent = {
      wakeWord: this.config.wakeWord || "hey computer",
      timestamp: Date.now(),
      confidence: 0.95
    }

    // Add to debug history
    this.detectionEvents.push(testEvent)
    if (this.detectionEvents.length > this.maxEventHistory) {
      this.detectionEvents = this.detectionEvents.slice(-this.maxEventHistory)
    }

    // Add test transcript
    this.recentTranscripts.push({
      transcript: `test ${testEvent.wakeWord} simulation`,
      timestamp: testEvent.timestamp,
      matched: true
    })
    if (this.recentTranscripts.length > this.maxEventHistory) {
      this.recentTranscripts = this.recentTranscripts.slice(-this.maxEventHistory)
    }

    diagnosticsService.logInfo("wake-word-service", `Simulated wake word detection: ${testEvent.wakeWord}`)
    this.emit("wakeWordDetected", testEvent)
  }
}

// Singleton instance
export const wakeWordService = new WakeWordService()
