import { app, Menu } from "electron"
import { electronApp, optimizer } from "@electron-toolkit/utils"
import {
  createMainWindow,
  createPanelWindow,
  createSetupWindow,
  makePanelWindowClosable,
  WINDOWS,
} from "./window"
import { listenToKeyboardEvents } from "./keyboard"
import { registerIpcMain } from "@egoist/tipc/main"
import { router } from "./tipc"
import { registerServeProtocol, registerServeSchema } from "./serve"
import { createAppMenu } from "./menu"
import { initTray } from "./tray"
import { isAccessibilityGranted } from "./utils"
import { mcpService } from "./mcp-service"
import { wakeWordService } from "./wake-word-service"

registerServeSchema()

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  // Set app user model id for windows
  electronApp.setAppUserModelId(process.env.APP_ID)

  const accessibilityGranted = isAccessibilityGranted()

  Menu.setApplicationMenu(createAppMenu())

  registerIpcMain(router)

  registerServeProtocol()

  if (accessibilityGranted) {
    createMainWindow()
  } else {
    createSetupWindow()
  }

  createPanelWindow()

  listenToKeyboardEvents()

  initTray()

  // Initialize MCP service on app startup
  mcpService.initialize().catch((error) => {
    console.error("Failed to initialize MCP service on startup:", error)
  })

  // Initialize wake word service on app startup
  wakeWordService.initialize().catch((error) => {
    console.error("Failed to initialize wake word service on startup:", error)
  })

  // Set up wake word detection event handler
  wakeWordService.on("wakeWordDetected", async (event) => {
    console.log("Wake word detected:", event)

    // Import window functions dynamically to avoid circular dependencies
    const { showPanelWindowAndStartRecording } = await import("./window")

    // Trigger recording when wake word is detected
    await showPanelWindowAndStartRecording()
  })

  import("./updater").then((res) => res.init()).catch(console.error)

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on("browser-window-created", (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  app.on("activate", function () {
    if (accessibilityGranted) {
      if (!WINDOWS.get("main")) {
        createMainWindow()
      }
    } else {
      if (!WINDOWS.get("setup")) {
        createSetupWindow()
      }
    }
  })

  app.on("before-quit", async () => {
    makePanelWindowClosable()

    // Cleanup services
    try {
      await wakeWordService.cleanup()
    } catch (error) {
      console.error("Failed to cleanup wake word service:", error)
    }

    try {
      await mcpService.cleanup()
    } catch (error) {
      console.error("Failed to cleanup MCP service:", error)
    }
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit()
  }
})

// In this file you can include the rest of your app"s specific main process
// code. You can also put them in separate files and require them here.
